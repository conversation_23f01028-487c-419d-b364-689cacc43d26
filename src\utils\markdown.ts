import MarkdownIt from 'markdown-it';

// Khởi tạo markdown-it với cấu hình tối ưu cho product descriptions
const md = new MarkdownIt({
  html: true,        // Cho phép HTML tags trong markdown
  linkify: true,     // Tự động convert URLs thành links
  typographer: true, // Beautify quotes, dashes, ellipsis
  breaks: false,     // Không convert \n thành <br> (giữ behavior chuẩn)
});

// Cấu hình linkify để mở links trong tab mới
md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  // Thêm target="_blank" và rel="noopener noreferrer" cho security
  const aIndex = tokens[idx].attrIndex('target');
  
  if (aIndex < 0) {
    tokens[idx].attrPush(['target', '_blank']);
  } else {
    tokens[idx].attrs![aIndex][1] = '_blank';
  }
  
  const relIndex = tokens[idx].attrIndex('rel');
  if (relIndex < 0) {
    tokens[idx].attrPush(['rel', 'noopener noreferrer']);
  } else {
    tokens[idx].attrs![relIndex][1] = 'noopener noreferrer';
  }

  // Render token với default renderer
  return self.renderToken(tokens, idx, options);
};

/**
 * Render markdown text thành HTML
 * @param markdownText - Text markdown cần render
 * @returns HTML string đã được render
 */
export function renderMarkdown(markdownText: string): string {
  if (!markdownText || typeof markdownText !== 'string') {
    return '';
  }
  
  try {
    return md.render(markdownText);
  } catch (error) {
    console.error('Error rendering markdown:', error);
    // Fallback về plain text nếu có lỗi
    return markdownText;
  }
}

/**
 * Render inline markdown (không wrap trong <p> tags)
 * @param markdownText - Text markdown cần render inline
 * @returns HTML string đã được render inline
 */
export function renderMarkdownInline(markdownText: string): string {
  if (!markdownText || typeof markdownText !== 'string') {
    return '';
  }
  
  try {
    return md.renderInline(markdownText);
  } catch (error) {
    console.error('Error rendering inline markdown:', error);
    // Fallback về plain text nếu có lỗi
    return markdownText;
  }
}

/**
 * Kiểm tra xem text có chứa markdown syntax không
 * @param text - Text cần kiểm tra
 * @returns true nếu có markdown syntax
 */
export function hasMarkdownSyntax(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  
  // Các pattern markdown phổ biến
  const markdownPatterns = [
    /\*\*.*?\*\*/,     // Bold **text**
    /\*.*?\*/,         // Italic *text*
    /__.*?__/,         // Bold __text__
    /_.*?_/,           // Italic _text_
    /`.*?`/,           // Code `text`
    /```[\s\S]*?```/,  // Code blocks
    /^#{1,6}\s/m,      // Headers
    /^\s*[-*+]\s/m,    // Lists
    /^\s*\d+\.\s/m,    // Numbered lists
    /\[.*?\]\(.*?\)/,  // Links [text](url)
    /!\[.*?\]\(.*?\)/, // Images ![alt](url)
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
}
