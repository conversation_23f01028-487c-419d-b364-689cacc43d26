globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,b as addAttribute,d as renderTemplate,r as renderComponent}from"../chunks/astro/server_BgKLHZ62.mjs";import{$ as $$Layout}from"../chunks/Layout_Bk587Jiw.mjs";export{renderers}from"../renderers.mjs";const $$Astro$1=createAstro("https://infpik.store"),$$CustomerPortalLink=createComponent(((e,r,t)=>{const s=e.createAstro($$Astro$1,r,t);s.self=$$CustomerPortalLink;const{variant:a="button",showEmailForm:o=!0,className:l=""}=s.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`customer-portal-section ${l}`,"class")}> ${"card"===a&&renderTemplate`<div class="bg-gradient-to-br from-primary-50 to-accent-50 border border-primary-200 rounded-xl p-6 text-center"> <div class="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg> </div> <h3 class="text-lg font-semibold text-primary-900 mb-2">Access Your Orders</h3> <p class="text-primary-700 mb-4">View your purchase history, downloads, and manage your account</p> <!-- Direct portal link --> <a href="/api/portal-redirect" class="btn-primary mb-4"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg>
Go to Customer Portal
</a> ${o&&renderTemplate`<div class="border-t border-primary-200 pt-4"> <p class="text-sm text-primary-600 mb-3">Or enter your email for quick access:</p> <form action="/api/portal-redirect" method="get" class="flex gap-2"> <input type="email" name="email" placeholder="<EMAIL>" class="flex-1 px-3 py-2 border border-primary-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 bg-white text-primary-900" required> <button type="submit" class="px-4 py-2 bg-accent-600 text-white rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors">
Access
</button> </form> </div>`} </div>`} ${"button"===a&&renderTemplate`<a href="/api/portal-redirect" class="btn-secondary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg>
My Orders
</a>`} ${"link"===a&&renderTemplate`<a href="/api/portal-redirect" class="inline-flex items-center gap-2 text-accent-600 hover:text-accent-700 font-medium transition-colors"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg>
View Order History
</a>`} </div>`}),"D:/code/image/polar-image-store/src/components/CustomerPortalLink.astro",void 0),$$Astro=createAstro("https://infpik.store"),$$Success=createComponent(((e,r,t)=>{const s=e.createAstro($$Astro,r,t);s.self=$$Success;const a=s.url.searchParams.get("checkout_id");return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Purchase Successful - InfPik",description:"Thank you for your purchase!"},{default:e=>renderTemplate` ${maybeRenderHead()}<section class="flex justify-center items-center min-h-[60vh] py-8"> <div class="text-center max-w-2xl mx-4 p-8 bg-white rounded-2xl shadow-xl border border-gray-100"> <div class="text-6xl mb-6"> <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path> </svg> </div> </div> <h1 class="text-4xl font-bold text-green-600 mb-4">Purchase Successful!</h1> <p class="text-xl text-gray-600 mb-8 leading-relaxed">
Thank you for your purchase. Your digital images are now available for download.
</p> ${a&&renderTemplate`<div class="bg-green-50 border-l-4 border-green-500 p-4 rounded-lg mb-8"> <p class="text-green-800"> <span class="font-semibold">Order ID:</span> <span class="font-mono text-sm">${a}</span> </p> </div>`} <div class="text-left mb-8"> <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">What's Next?</h2> <ul class="space-y-4"> <li class="flex items-start gap-3"> <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path> </svg> </div> <span class="text-gray-700">Check your email for download links and receipt</span> </li> <li class="flex items-start gap-3"> <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path> </svg> </div> <span class="text-gray-700">Download your high-resolution images</span> </li> <li class="flex items-start gap-3"> <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path> </svg> </div> <span class="text-gray-700">Review the license terms for usage rights</span> </li> </ul> </div> <!-- Customer Portal Access --> <div class="mb-8"> ${renderComponent(e,"CustomerPortalLink",$$CustomerPortalLink,{variant:"card",showEmailForm:!0})} </div> <div class="flex flex-col sm:flex-row gap-4 justify-center"> <a href="/products" class="btn-secondary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse More Images
</a> <a href="/" class="btn-primary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path> </svg>
Back to Home
</a> </div> </div> </section> `})}`}),"D:/code/image/polar-image-store/src/pages/success.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/success.astro",$$url="/success",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Success,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};