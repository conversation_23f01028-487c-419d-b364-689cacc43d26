globalThis.process??={},globalThis.process.env??={};import{g as decode<PERSON><PERSON>}from"./chunks/astro/server_BgKLHZ62.mjs";import"./chunks/astro-designed-error-pages_BAUzKiqr.mjs";import{N as NOOP_MIDDLEWARE_FN}from"./chunks/noop-middleware_qkLZ8MMB.mjs";function sanitizeParams(e){return Object.fromEntries(Object.entries(e).map((([e,t])=>"string"==typeof t?[e,t.normalize().replace(/#/g,"%23").replace(/\?/g,"%3F")]:[e,t])))}function getParameter(e,t){if(e.spread)return t[e.content.slice(3)]||"";if(e.dynamic){if(!t[e.content])throw new TypeError(`Missing parameter: ${e.content}`);return t[e.content]}return e.content.normalize().replace(/\?/g,"%3F").replace(/#/g,"%23").replace(/%5B/g,"[").replace(/%5D/g,"]")}function getSegment(e,t){const a=e.map((e=>getParameter(e,t))).join("");return a?"/"+a:""}function getRouteGenerator(e,t){return a=>{const s=sanitizeParams(a);let r="";"always"===t&&e.length&&(r="/");return e.map((e=>getSegment(e,s))).join("")+r||"/"}}function deserializeRouteData(e){return{route:e.route,type:e.type,pattern:new RegExp(e.pattern),params:e.params,component:e.component,generate:getRouteGenerator(e.segments,e._meta.trailingSlash),pathname:e.pathname||void 0,segments:e.segments,prerender:e.prerender,redirect:e.redirect,redirectRoute:e.redirectRoute?deserializeRouteData(e.redirectRoute):void 0,fallbackRoutes:e.fallbackRoutes.map((e=>deserializeRouteData(e))),isIndex:e.isIndex,origin:e.origin}}function deserializeManifest(e){const t=[];for(const a of e.routes){t.push({...a,routeData:deserializeRouteData(a.routeData)});a.routeData=deserializeRouteData(a.routeData)}const a=new Set(e.assets),s=new Map(e.componentMetadata),r=new Map(e.inlinedScripts),o=new Map(e.clientDirectives),n=new Map(e.serverIslandNameMap),i=decodeKey(e.key);return{middleware:()=>({onRequest:NOOP_MIDDLEWARE_FN}),...e,assets:a,componentMetadata:s,inlinedScripts:r,clientDirectives:o,routes:t,serverIslandNameMap:n,key:i}}const manifest=deserializeManifest({hrefRoot:"file:///D:/code/image/polar-image-store/",cacheDir:"file:///D:/code/image/polar-image-store/node_modules/.astro/",outDir:"file:///D:/code/image/polar-image-store/dist/",srcDir:"file:///D:/code/image/polar-image-store/src/",publicDir:"file:///D:/code/image/polar-image-store/public/",buildClientDir:"file:///D:/code/image/polar-image-store/dist/",buildServerDir:"file:///D:/code/image/polar-image-store/dist/_worker.js/",adapterName:"@astrojs/cloudflare",routes:[{file:"",links:[],scripts:[],styles:[],routeData:{type:"page",component:"_server-islands.astro",params:["name"],segments:[[{content:"_server-islands",dynamic:!1,spread:!1}],[{content:"name",dynamic:!0,spread:!1}]],pattern:"^\\/_server-islands\\/([^/]+?)\\/?$",prerender:!1,isIndex:!1,fallbackRoutes:[],route:"/_server-islands/[name]",origin:"internal",_meta:{trailingSlash:"ignore"}}},{file:"about/index.html",links:[],scripts:[],styles:[],routeData:{route:"/about",isIndex:!1,type:"page",pattern:"^\\/about\\/?$",segments:[[{content:"about",dynamic:!1,spread:!1}]],params:[],component:"src/pages/about.astro",pathname:"/about",prerender:!0,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"privacy/index.html",links:[],scripts:[],styles:[],routeData:{route:"/privacy",isIndex:!1,type:"page",pattern:"^\\/privacy\\/?$",segments:[[{content:"privacy",dynamic:!1,spread:!1}]],params:[],component:"src/pages/privacy.astro",pathname:"/privacy",prerender:!0,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"terms/index.html",links:[],scripts:[],styles:[],routeData:{route:"/terms",isIndex:!1,type:"page",pattern:"^\\/terms\\/?$",segments:[[{content:"terms",dynamic:!1,spread:!1}]],params:[],component:"src/pages/terms.astro",pathname:"/terms",prerender:!0,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{type:"endpoint",isIndex:!1,route:"/_image",pattern:"^\\/_image\\/?$",segments:[[{content:"_image",dynamic:!1,spread:!1}]],params:[],component:"node_modules/astro/dist/assets/endpoint/generic.js",pathname:"/_image",prerender:!1,fallbackRoutes:[],origin:"internal",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"},{type:"inline",content:"@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}.text-8xl[data-astro-cid-zetdm5md],.text-9xl[data-astro-cid-zetdm5md]{animation:float 3s ease-in-out infinite}.inline-flex[data-astro-cid-zetdm5md]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #6366f126}\n"}],routeData:{route:"/404",isIndex:!1,type:"page",pattern:"^\\/404\\/?$",segments:[[{content:"404",dynamic:!1,spread:!1}]],params:[],component:"src/pages/404.astro",pathname:"/404",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/checkout",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/checkout\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"checkout",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/checkout.ts",pathname:"/api/checkout",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/image-proxy",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/image-proxy\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"image-proxy",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/image-proxy.ts",pathname:"/api/image-proxy",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/portal-redirect",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/portal-redirect\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"portal-redirect",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/portal-redirect.ts",pathname:"/api/portal-redirect",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/products",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/products\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"products",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/products.ts",pathname:"/api/products",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/search",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/search\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"search",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/search.ts",pathname:"/api/search",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/tags",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/tags\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"tags",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/tags.ts",pathname:"/api/tags",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/api/webhooks",isIndex:!1,type:"endpoint",pattern:"^\\/api\\/webhooks\\/?$",segments:[[{content:"api",dynamic:!1,spread:!1}],[{content:"webhooks",dynamic:!1,spread:!1}]],params:[],component:"src/pages/api/webhooks.ts",pathname:"/api/webhooks",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"}],routeData:{route:"/products/category/[category]",isIndex:!1,type:"page",pattern:"^\\/products\\/category\\/([^/]+?)\\/?$",segments:[[{content:"products",dynamic:!1,spread:!1}],[{content:"category",dynamic:!1,spread:!1}],[{content:"category",dynamic:!0,spread:!1}]],params:["category"],component:"src/pages/products/category/[category].astro",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"}],routeData:{route:"/products/tag/[tag]",isIndex:!1,type:"page",pattern:"^\\/products\\/tag\\/([^/]+?)\\/?$",segments:[[{content:"products",dynamic:!1,spread:!1}],[{content:"tag",dynamic:!1,spread:!1}],[{content:"tag",dynamic:!0,spread:!1}]],params:["tag"],component:"src/pages/products/tag/[tag].astro",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"},{type:"inline",content:"@keyframes fade-in{0%{opacity:0}to{opacity:1}}.animate-fade-in[data-astro-cid-gjhjmbi3]{animation:fade-in .3s ease}#lightbox[data-astro-cid-gjhjmbi3].active{display:flex!important;align-items:center;justify-content:center}#lightboxImage[data-astro-cid-gjhjmbi3]{border-radius:1rem}@media (max-width: 768px){#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-left-16{left:10px}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-right-16{right:10px}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-top-12{top:10px;right:10px;font-size:1.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-bottom-12{bottom:10px}}.scrollbar-hide[data-astro-cid-4j3m3ndg]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-4j3m3ndg]::-webkit-scrollbar{display:none}.line-clamp-2[data-astro-cid-4j3m3ndg]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}details[data-astro-cid-wcd6ad2o][open] summary[data-astro-cid-wcd6ad2o]{border-bottom:1px solid #e5e7eb}summary[data-astro-cid-wcd6ad2o]::-webkit-details-marker{display:none}summary[data-astro-cid-wcd6ad2o]::marker{display:none}\n"}],routeData:{route:"/products/[slug]",isIndex:!1,type:"page",pattern:"^\\/products\\/([^/]+?)\\/?$",segments:[[{content:"products",dynamic:!1,spread:!1}],[{content:"slug",dynamic:!0,spread:!1}]],params:["slug"],component:"src/pages/products/[slug].astro",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"}],routeData:{route:"/products",isIndex:!0,type:"page",pattern:"^\\/products\\/?$",segments:[[{content:"products",dynamic:!1,spread:!1}]],params:[],component:"src/pages/products/index.astro",pathname:"/products",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[],routeData:{route:"/sitemap.xml",isIndex:!1,type:"endpoint",pattern:"^\\/sitemap\\.xml\\/?$",segments:[[{content:"sitemap.xml",dynamic:!1,spread:!1}]],params:[],component:"src/pages/sitemap.xml.ts",pathname:"/sitemap.xml",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"}],routeData:{route:"/success",isIndex:!1,type:"page",pattern:"^\\/success\\/?$",segments:[[{content:"success",dynamic:!1,spread:!1}]],params:[],component:"src/pages/success.astro",pathname:"/success",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"}],routeData:{route:"/trending",isIndex:!1,type:"page",pattern:"^\\/trending\\/?$",segments:[[{content:"trending",dynamic:!1,spread:!1}]],params:[],component:"src/pages/trending.astro",pathname:"/trending",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}},{file:"",links:[],scripts:[],styles:[{type:"external",src:"/_astro/about.fVJ_8kwW.css"},{type:"inline",content:".bg-grid-pattern[data-astro-cid-bbe6dxrz]{background-image:linear-gradient(rgba(0,0,0,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 1px);background-size:20px 20px}.scrollbar-hide[data-astro-cid-bbe6dxrz]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-bbe6dxrz]::-webkit-scrollbar{display:none}.scrollbar-hide[data-astro-cid-wy47okmw]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-wy47okmw]::-webkit-scrollbar{display:none}\n"}],routeData:{route:"/",isIndex:!0,type:"page",pattern:"^\\/$",segments:[],params:[],component:"src/pages/index.astro",pathname:"/",prerender:!1,fallbackRoutes:[],distURL:[],origin:"project",_meta:{trailingSlash:"ignore"}}}],site:"https://infpik.store",base:"/",trailingSlash:"ignore",compressHTML:!0,componentMetadata:[["D:/code/image/polar-image-store/src/pages/404.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/about.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/index.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/privacy.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/products/[slug].astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/products/category/[category].astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/products/index.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/success.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/terms.astro",{propagation:"none",containsHead:!0}],["D:/code/image/polar-image-store/src/pages/trending.astro",{propagation:"none",containsHead:!0}]],renderers:[],clientDirectives:[["idle",'(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value=="object"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};"requestIdleCallback"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event("astro:idle"));})();'],["load",'(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event("astro:load"));})();'],["media",'(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener("change",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event("astro:media"));})();'],["only",'(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event("astro:only"));})();'],["visible",'(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value=="object"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event("astro:visible"));})();']],entryModules:{"\0@astrojs-ssr-adapter":"<EMAIL>","\0noop-actions":"_noop-actions.mjs","\0@astro-renderers":"renderers.mjs","\0@astro-page:src/pages/404@_@astro":"pages/404.astro.mjs","\0@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\0@astro-page:src/pages/api/checkout@_@ts":"pages/api/checkout.astro.mjs","\0@astro-page:src/pages/api/image-proxy@_@ts":"pages/api/image-proxy.astro.mjs","\0@astro-page:src/pages/api/portal-redirect@_@ts":"pages/api/portal-redirect.astro.mjs","\0@astro-page:src/pages/api/products@_@ts":"pages/api/products.astro.mjs","\0@astro-page:src/pages/api/search@_@ts":"pages/api/search.astro.mjs","\0@astro-page:src/pages/api/tags@_@ts":"pages/api/tags.astro.mjs","\0@astro-page:src/pages/api/webhooks@_@ts":"pages/api/webhooks.astro.mjs","\0@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\0@astro-page:src/pages/products/category/[category]@_@astro":"pages/products/category/_category_.astro.mjs","\0@astro-page:src/pages/products/tag/[tag]@_@astro":"pages/products/tag/_tag_.astro.mjs","\0@astro-page:src/pages/products/index@_@astro":"pages/products.astro.mjs","\0@astro-page:src/pages/sitemap.xml@_@ts":"pages/sitemap.xml.astro.mjs","\0@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\0@astro-page:src/pages/trending@_@astro":"pages/trending.astro.mjs","\0@astrojs-ssr-virtual-entry":"index.js","\0astro-internal:middleware":"_astro-internal_middleware.mjs","\0@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","\0@astro-page:src/pages/success@_@astro":"pages/success.astro.mjs","\0@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\0@astro-page:src/pages/products/[slug]@_@astro":"pages/products/_slug_.astro.mjs","D:/code/image/polar-image-store/src/utils/imageOptimization.ts":"chunks/imageOptimization_Dac7PwDt.mjs","D:/code/image/polar-image-store/node_modules/unstorage/drivers/cloudflare-kv-binding.mjs":"chunks/cloudflare-kv-binding_DMly_2Gl.mjs","\0@astrojs-manifest":"manifest_B-OBfhMO.mjs","D:/code/image/polar-image-store/node_modules/@astrojs/cloudflare/dist/entrypoints/image-service.js":"chunks/image-service_Db1gBhpI.mjs","D:/code/image/polar-image-store/src/pages/404.astro?astro&type=script&index=0&lang.ts":"_astro/404.astro_astro_type_script_index_0_lang.bRgoiY3O.js","D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang.XUrZq91Z.js","D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts":"_astro/Layout.astro_astro_type_script_index_0_lang.BFTyHI5A.js","D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts":"_astro/Hero.astro_astro_type_script_index_0_lang.BSqBxHFd.js","D:/code/image/polar-image-store/src/components/TagNavigation.astro?astro&type=script&index=0&lang.ts":"_astro/TagNavigation.astro_astro_type_script_index_0_lang.DW9diLzm.js","D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts":"_astro/ImageGallery.astro_astro_type_script_index_0_lang.DUZtGiXG.js","D:/code/image/polar-image-store/src/components/SearchModal.astro?astro&type=script&index=0&lang.ts":"_astro/SearchModal.astro_astro_type_script_index_0_lang.CpALRN_D.js","astro:scripts/before-hydration.js":""},inlinedScripts:[["D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts",'document.addEventListener("DOMContentLoaded",()=>{window.addEventListener("categoryChange",e=>{const l=e.detail.categoryId;n(l)});function n(e){document.querySelectorAll(".product-item").forEach(t=>{const s=t.dataset.category,a=t.dataset.featured==="true";e==="all"||s===e?e==="all"&&!a?t.style.display="none":t.style.display="block":t.style.display="none"});const o=document.querySelector("#view-all-products-btn");if(o&&e!=="all"){const t=r(e);o.innerHTML=`\n          View All ${t} Images\n          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />\n          </svg>\n        `,o.href=`/products/category/${e}`}else o&&e==="all"&&(o.innerHTML=`\n          View All Images\n          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />\n          </svg>\n        `,o.href="/products")}function r(e){return e.split("-").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")}});'],["D:/code/image/polar-image-store/src/components/TagNavigation.astro?astro&type=script&index=0&lang.ts",'document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("tagScroll"),c=document.querySelectorAll(".tag-tab");if(!e)return;let r=!1,o,n;e.addEventListener("touchstart",t=>{r=!0,o=t.touches[0].pageX-e.offsetLeft,n=e.scrollLeft}),e.addEventListener("touchend",()=>{r=!1}),e.addEventListener("touchmove",t=>{if(!r)return;const a=(t.touches[0].pageX-e.offsetLeft-o)*2;e.scrollLeft=n-a}),e.addEventListener("mousedown",t=>{r=!0,o=t.pageX-e.offsetLeft,n=e.scrollLeft,e.style.cursor="grabbing"}),e.addEventListener("mouseleave",()=>{r=!1,e.style.cursor="grab"}),e.addEventListener("mouseup",()=>{r=!1,e.style.cursor="grab"}),e.addEventListener("mousemove",t=>{if(!r)return;t.preventDefault();const a=(t.pageX-e.offsetLeft-o)*2;e.scrollLeft=n-a}),e.style.cursor="grab",c.forEach(t=>{t.addEventListener("click",s=>{const a=s.currentTarget.dataset.tag;c.forEach(d=>{d.classList.remove("bg-accent-600","text-white","shadow-md"),d.classList.add("bg-primary-50","text-primary-900","border","border-primary-200")}),s.currentTarget.classList.remove("bg-primary-50","text-primary-900","border","border-primary-200"),s.currentTarget.classList.add("bg-accent-600","text-white","shadow-md"),window.dispatchEvent(new CustomEvent("tagChange",{detail:{tagId:a}}))})})});'],["D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts",'let i=0,n=[];document.addEventListener("DOMContentLoaded",function(){const t=document.getElementById("mainImage"),e=document.querySelectorAll("[data-thumbnail-index]");n=[],t&&n.push(t.src),e.forEach(a=>{const o=a.getAttribute("data-image-src");if(o){const r=o.includes("/cdn-cgi/image/")?o.replace(/\\/cdn-cgi\\/image\\/[^\\/]+\\//,"/cdn-cgi/image/width=1200,height=900,quality=95,format=auto,fit=cover/"):`/cdn-cgi/image/width=1024,height=1024,quality=95,format=auto,fit=cover/${o}`;n.push(r)}}),t&&t.addEventListener("click",function(){const a=parseInt(this.getAttribute("data-lightbox-index"))||0;d(a)}),e.forEach(a=>{a.addEventListener("click",function(){const o=parseInt(this.getAttribute("data-lightbox-index"));o!==null&&d(o)})});const c=document.getElementById("prevButton"),g=document.getElementById("nextButton");c&&c.addEventListener("click",m),g&&g.addEventListener("click",s)});function d(t){const e=document.getElementById("lightbox"),c=document.getElementById("lightboxImage"),g=document.getElementById("imageCounter");e&&c&&n.length>0&&(i=t,c.src=n[i],c.alt=`Image ${i+1}`,g.textContent=`${i+1} / ${n.length}`,e.classList.add("active"),document.body.style.overflow="hidden")}function l(){const t=document.getElementById("lightbox");t&&(t.classList.remove("active"),document.body.style.overflow="auto")}function m(){n.length>1&&(i=(i-1+n.length)%n.length,u())}function s(){n.length>1&&(i=(i+1)%n.length,u())}function u(){const t=document.getElementById("lightboxImage"),e=document.getElementById("imageCounter");t&&e&&(t.src=n[i],t.alt=`Image ${i+1}`,e.textContent=`${i+1} / ${n.length}`)}document.addEventListener("keydown",function(t){const e=document.getElementById("lightbox");if(e&&e.classList.contains("active"))switch(t.key){case"Escape":l();break;case"ArrowLeft":m();break;case"ArrowRight":s();break}});document.addEventListener("click",function(t){const e=document.getElementById("lightbox");t.target===e&&l()});']],assets:["/_astro/about.fVJ_8kwW.css","/favicon.svg","/llms.txt","/logo.svg","/manifest.json","/og-image.jpg","/placeholder-image.svg","/robots.txt","/sw.js","/fonts/inter.woff2","/_astro/404.astro_astro_type_script_index_0_lang.bRgoiY3O.js","/_astro/Hero.astro_astro_type_script_index_0_lang.BSqBxHFd.js","/_astro/Layout.astro_astro_type_script_index_0_lang.BFTyHI5A.js","/_astro/SearchModal.astro_astro_type_script_index_0_lang.CpALRN_D.js","/_worker.js/index.js","/_worker.js/renderers.mjs","/_worker.js/<EMAIL>","/_worker.js/_astro-internal_middleware.mjs","/_worker.js/_noop-actions.mjs","/_worker.js/chunks/astro-designed-error-pages_BAUzKiqr.mjs","/_worker.js/chunks/astro_T7oRQM_v.mjs","/_worker.js/chunks/cloudflare-kv-binding_DMly_2Gl.mjs","/_worker.js/chunks/image-service_Db1gBhpI.mjs","/_worker.js/chunks/imageOptimization_Dac7PwDt.mjs","/_worker.js/chunks/index_C4LTQoxk.mjs","/_worker.js/chunks/index_kkHkWl4A.mjs","/_worker.js/chunks/Layout_Bk587Jiw.mjs","/_worker.js/chunks/noop-middleware_qkLZ8MMB.mjs","/_worker.js/chunks/path_h5kZAkfu.mjs","/_worker.js/chunks/polar_D7XkB6p_.mjs","/_worker.js/chunks/ProductCard_CqzLMWoY.mjs","/_worker.js/chunks/sdk_DEQ9AU5A.mjs","/_worker.js/chunks/server_CJMbZFIY.mjs","/_worker.js/chunks/StructuredData__W5kXoJd.mjs","/_worker.js/chunks/_astro_assets_BJ9yS0_G.mjs","/_worker.js/pages/404.astro.mjs","/_worker.js/pages/about.astro.mjs","/_worker.js/pages/index.astro.mjs","/_worker.js/pages/privacy.astro.mjs","/_worker.js/pages/products.astro.mjs","/_worker.js/pages/sitemap.xml.astro.mjs","/_worker.js/pages/success.astro.mjs","/_worker.js/pages/terms.astro.mjs","/_worker.js/pages/trending.astro.mjs","/_worker.js/pages/_image.astro.mjs","/_worker.js/_astro/about.fVJ_8kwW.css","/_worker.js/chunks/astro/server_BgKLHZ62.mjs","/_worker.js/pages/api/checkout.astro.mjs","/_worker.js/pages/api/image-proxy.astro.mjs","/_worker.js/pages/api/portal-redirect.astro.mjs","/_worker.js/pages/api/products.astro.mjs","/_worker.js/pages/api/search.astro.mjs","/_worker.js/pages/api/tags.astro.mjs","/_worker.js/pages/api/webhooks.astro.mjs","/_worker.js/pages/products/_slug_.astro.mjs","/_worker.js/pages/products/category/_category_.astro.mjs","/_worker.js/pages/products/tag/_tag_.astro.mjs","/about/index.html","/privacy/index.html","/terms/index.html"],buildFormat:"directory",checkOrigin:!0,serverIslandNameMap:[],key:"T/AxHjqqEn7T1MVPdHxnsgEH1gnigwp9rRY+VDtxFTw=",sessionConfig:{driver:"cloudflare-kv-binding",options:{binding:"SESSION"}}});manifest.sessionConfig&&(manifest.sessionConfig.driverModule=()=>import("./chunks/cloudflare-kv-binding_DMly_2Gl.mjs"));export{manifest};